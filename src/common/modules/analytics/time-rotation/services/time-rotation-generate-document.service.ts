import { Injectable } from '@nestjs/common';
import { Branch } from '../../../database/entities/branch.entity';
import { Zone } from '../../../database/entities/zone.entity';
import { Role } from '../../../database/entities/role.entity';
import { User } from '../../../database/entities/user.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { Checkpoint } from '../../../database/entities/checkpoint.entity';
import { Device } from '../../../database/entities/device.entity';
import { TimeAndRotation } from '../interfaces/time-rotation-analytic.interface';
import { RotationMethod } from '../../../../interfaces/rotation-method.enum';

// Import shared utilities
import { PdfGenerator } from '../../shared/utils/pdf-generator.util';
import { ExcelGenerator } from '../../shared/utils/excel-generator.util';
import {
  DataTransformationUtils,
  DateRangeFilter,
  DocumentDisplayData,
  DocumentGenerationResult,
  FilterDisplayData,
  PdfCustomSectionRenderer,
  ExcelCustomSectionRenderer,
  DocumentUtils,
} from '../../shared/utils/data-transformation.util';

export interface TimeRotationFilters {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  rotationInterval: number | null;
  rotationMethod: RotationMethod | null;
  branchId: number | null;
  zoneId: number | null;
  checkpointId: number | null;
  checkpointLabels: string[] | null;
  branch?: Branch;
  zone?: Zone;
  checkpoint?: Checkpoint;
  timezone?: Timezone;
}

/**
 * Time Rotation specific data transformation utilities
 */
class TimeRotationDataTransformer {
  static transformToDisplayData(timeRotation: TimeAndRotation): {
    leftColumnData: DocumentDisplayData[];
    rightColumnData: DocumentDisplayData[];
  } {
    // Calculate summary statistics
    const totalRequired = timeRotation.rotations?.reduce((sum, rotation) => sum + (rotation.compliance || 0), 0) || 0;
    const totalCompleted = timeRotation.rotations?.reduce((sum, rotation) => sum + (rotation.completed || 0), 0) || 0;
    const complianceRate = totalRequired > 0 ? ((totalCompleted / totalRequired) * 100).toFixed(1) : '0.0';
    const totalIntervals = timeRotation.rotations?.reduce((total, rotation) => total + (rotation.intervals?.length || 0), 0) || 0;
    const completedIntervals = timeRotation.rotations?.reduce((total, rotation) =>
      total + (rotation.intervals?.filter(interval => interval.rotation > 0).length || 0), 0) || 0;
    const intervalCompletionRate = totalIntervals > 0 ? ((completedIntervals / totalIntervals) * 100).toFixed(1) : '0.0';

    const leftColumnData: DocumentDisplayData[] = [
      {
        label: 'Checkpoint ID',
        value: timeRotation.checkpoint_id?.toString() || '-',
      },
      {
        label: 'Checkpoint Name',
        value: timeRotation.checkpoint_name || '-',
      },
      {
        label: 'Rotation Interval',
        value: TimeRotationDataTransformer.formatMinutesToHumanReadable(timeRotation.interval || 0),
      },
      {
        label: 'Total Required',
        value: totalRequired.toString(),
      },
      {
        label: 'Total Completed',
        value: totalCompleted.toString(),
      },
    ];

    const rightColumnData: DocumentDisplayData[] = [
      {
        label: 'Rotation Method',
        value: timeRotation.method || '-',
      },
      {
        label: 'Compliance Rate',
        value: `${complianceRate}%`,
      },
      {
        label: 'Total Intervals',
        value: totalIntervals.toString(),
      },
      {
        label: 'Completed Intervals',
        value: completedIntervals.toString(),
      },
      {
        label: 'Interval Completion',
        value: `${intervalCompletionRate}%`,
      },
    ];

    return { leftColumnData, rightColumnData };
  }

  static buildDataRows(timeRotation: TimeAndRotation): string[][] {
    // Calculate summary statistics
    const totalRequired = timeRotation.rotations?.reduce((sum, rotation) => sum + (rotation.compliance || 0), 0) || 0;
    const totalCompleted = timeRotation.rotations?.reduce((sum, rotation) => sum + (rotation.completed || 0), 0) || 0;
    const complianceRate = totalRequired > 0 ? ((totalCompleted / totalRequired) * 100).toFixed(1) : '0.0';
    const totalIntervals = timeRotation.rotations?.reduce((total, rotation) => total + (rotation.intervals?.length || 0), 0) || 0;
    const completedIntervals = timeRotation.rotations?.reduce((total, rotation) =>
      total + (rotation.intervals?.filter(interval => interval.rotation > 0).length || 0), 0) || 0;
    const intervalCompletionRate = totalIntervals > 0 ? ((completedIntervals / totalIntervals) * 100).toFixed(1) : '0.0';

    return [
      ['Checkpoint ID', timeRotation.checkpoint_id?.toString() || '-'],
      ['Checkpoint Name', timeRotation.checkpoint_name || '-'],
      ['Rotation Interval', TimeRotationDataTransformer.formatMinutesToHumanReadable(timeRotation.interval || 0)],
      ['Rotation Method', timeRotation.method || '-'],
      ['Total Required', totalRequired.toString()],
      ['Total Completed', totalCompleted.toString()],
      ['Compliance Rate', `${complianceRate}%`],
      ['Total Intervals', totalIntervals.toString()],
      ['Completed Intervals', completedIntervals.toString()],
      ['Interval Completion', `${intervalCompletionRate}%`],
    ];
  }

  static buildExcelRowData(timeRotation: TimeAndRotation): any[] {
    // Calculate summary statistics
    const totalRequired = timeRotation.rotations?.reduce((sum, rotation) => sum + (rotation.compliance || 0), 0) || 0;
    const totalCompleted = timeRotation.rotations?.reduce((sum, rotation) => sum + (rotation.completed || 0), 0) || 0;
    const complianceRate = totalRequired > 0 ? ((totalCompleted / totalRequired) * 100).toFixed(1) : '0.0';
    const totalIntervals = timeRotation.rotations?.reduce((total, rotation) => total + (rotation.intervals?.length || 0), 0) || 0;
    const completedIntervals = timeRotation.rotations?.reduce((total, rotation) =>
      total + (rotation.intervals?.filter(interval => interval.rotation > 0).length || 0), 0) || 0;
    const intervalCompletionRate = totalIntervals > 0 ? ((completedIntervals / totalIntervals) * 100).toFixed(1) : '0.0';

    return [
      timeRotation.checkpoint_id || '-',
      timeRotation.checkpoint_name || '-',
      TimeRotationDataTransformer.formatMinutesToHumanReadable(timeRotation.interval || 0),
      timeRotation.method || '-',
      totalRequired,
      totalCompleted,
      `${complianceRate}%`,
      totalIntervals,
      completedIntervals,
      `${intervalCompletionRate}%`,
    ];
  }

  static buildFilterData(filters: TimeRotationFilters): {
    leftFilters: FilterDisplayData[];
    rightFilters: FilterDisplayData[];
  } {
    const leftFilters = [
      {
        label: 'Start Date:',
        value: filters.startDate
          ? DataTransformationUtils.formatFilterDate(filters.startDate)
          : 'All Dates',
      },
      {
        label: 'End Date:',
        value: filters.endDate
          ? DataTransformationUtils.formatFilterDate(filters.endDate)
          : 'All Dates',
      },
      {
        label: 'Start Time:',
        value: filters.startTime || '00:00:00',
      },
      {
        label: 'End Time:',
        value: filters.endTime || '23:59:59',
      },
    ];

    const rightFilters = [
      {
        label: 'Rotation Interval:',
        value: filters.rotationInterval ? `${filters.rotationInterval} minutes` : 'All',
      },
      {
        label: 'Rotation Method:',
        value: filters.rotationMethod || 'All',
      },
      {
        label: 'Branch:',
        value: filters.branch ? filters.branch.branch_name : 'All Branches',
      },
      {
        label: 'Zone:',
        value: filters.zone ? filters.zone.zone_name : 'All Zones',
      },
    ];

    return { leftFilters, rightFilters };
  }

  static buildDateRangeFilter(filters: TimeRotationFilters): DateRangeFilter {
    return {
      startDate: filters.startDate,
      endDate: filters.endDate,
      startTime: filters.startTime,
      endTime: filters.endTime,
    };
  }

  static formatMinutesToHumanReadable(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    }
    return `${hours}h ${remainingMinutes}m`;
  }
}

/**
 * Creates a custom PDF section renderer for time rotation detailed data
 */
function createPdfCustomSectionRenderer(
  timeRotation: TimeAndRotation,
): PdfCustomSectionRenderer | undefined {
  return async (doc, currentY) => {
    let yPosition = currentY;
    const pageWidth = 595.28;
    const margin = 50;
    const contentWidth = pageWidth - 2 * margin;

    // Add detailed time rotation data section header
    doc.fontSize(14).fillColor('#1a237e').font('Helvetica-Bold')
      .text('DETAILED TIME ROTATION DATA', margin, yPosition, {
        width: contentWidth,
        align: 'center',
      });
    yPosition += 25;

    // Add separator line
    doc.moveTo(margin, yPosition)
      .lineTo(margin + contentWidth, yPosition)
      .lineWidth(1)
      .strokeColor('#1a237e')
      .stroke();
    yPosition += 20;

    // Checkpoint header with professional styling
    doc.fontSize(12).fillColor('#1a237e').font('Helvetica-Bold')
      .text(`Checkpoint: ${timeRotation.checkpoint_name}`, margin, yPosition);
    yPosition += 15;

    doc.fontSize(10).fillColor('#546e7a').font('Helvetica')
      .text(`ID: ${timeRotation.checkpoint_id} | Interval: ${TimeRotationDataTransformer.formatMinutesToHumanReadable(timeRotation.interval || 0)} | Method: ${timeRotation.method}`, margin, yPosition);
    yPosition += 25;

    // Process all intervals from all dates without grouping by date
    if (timeRotation.rotations && timeRotation.rotations.length > 0) {
      // Table headers with improved visibility
      const headerY = yPosition;

      // Header background with darker color for better contrast
      doc.rect(margin, headerY - 5, contentWidth, 22).fill('#2c3e50');

      // Header text with white color and proper positioning
      doc.fontSize(10).fillColor('#ffffff').font('Helvetica-Bold');
      doc.text('Interval', margin + 8, headerY + 2);
      doc.text('Time', margin + 75, headerY + 2);
      doc.text('Start', margin + 150, headerY + 2);
      doc.text('End', margin + 250, headerY + 2);
      doc.text('Total Scan', margin + 350, headerY + 2);
      doc.text('Status', margin + 430, headerY + 2);
      yPosition += 22;

      let intervalCounter = 1;

      // Process each date's intervals
      timeRotation.rotations.forEach((rotation) => {
        if (rotation.intervals && rotation.intervals.length > 0) {
          rotation.intervals.forEach((interval, intervalIndex) => {
            // Check if we need a new page
            if (yPosition > 720) {
              doc.addPage();
              yPosition = 50;

              // Re-add headers on new page with improved visibility
              doc.rect(margin, yPosition - 5, contentWidth, 22).fill('#2c3e50');
              doc.fontSize(10).fillColor('#ffffff').font('Helvetica-Bold');
              doc.text('Interval', margin + 8, yPosition + 2);
              doc.text('Time', margin + 75, yPosition + 2);
              doc.text('Start', margin + 150, yPosition + 2);
              doc.text('End', margin + 250, yPosition + 2);
              doc.text('Total Scan', margin + 350, yPosition + 2);
              doc.text('Status', margin + 430, yPosition + 2);
              yPosition += 22;
            }

            const rowY = yPosition;
            const bgColor = intervalCounter % 2 === 0 ? '#f8f9fa' : '#ffffff';

            // Row background
            doc.rect(margin, rowY - 2, contentWidth, 20).fill(bgColor);

            // Row data with proper spacing and black text
            doc.fontSize(9).fillColor('#000000').font('Helvetica');
            doc.text(`#${intervalCounter}`, margin + 8, rowY + 4);
            doc.text(interval.time || '-', margin + 75, rowY + 4);

            // Format start and end times without timezone
            const startTime = interval.dateTimeRange?.start ?
              new Date(interval.dateTimeRange.start).toLocaleString() : '-';
            const endTime = interval.dateTimeRange?.end ?
              new Date(interval.dateTimeRange.end).toLocaleString() : '-';

            doc.text(startTime, margin + 150, rowY + 4, { width: 95, ellipsis: true });
            doc.text(endTime, margin + 250, rowY + 4, { width: 95, ellipsis: true });

            // Total scan count
            const totalScan = interval.rotation || 0;
            doc.text(totalScan.toString(), margin + 350, rowY + 4);

            // Status with color coding
            const status = interval.rotation > 0 ? 'Completed' : 'Not Completed';
            const statusColor = interval.rotation > 0 ? '#27ae60' : '#e74c3c';
            doc.fillColor(statusColor).font('Helvetica-Bold')
              .text(status, margin + 430, rowY + 4);

            yPosition += 20;
            intervalCounter++;
          });
        }
      });
    }

    yPosition += 15;
    return yPosition;
  };
}

/**
 * Creates a custom Excel section renderer for time rotation detailed data
 */
function createExcelCustomSectionRenderer(
  timeRotation: TimeAndRotation,
): ExcelCustomSectionRenderer {
  return async (worksheet, startRow) => {
    let currentRow = startRow;

    // Only add detailed intervals if there are intervals to show
    let intervalCounter = 1;
    let hasIntervals = false;

    if (timeRotation.rotations && timeRotation.rotations.length > 0) {
      timeRotation.rotations.forEach((rotation) => {
        if (rotation.intervals && rotation.intervals.length > 0) {
          hasIntervals = true;
        }
      });
    }

    if (hasIntervals) {
      // Detailed intervals table headers with lighter color (sub-table style)
      const detailHeaders = ['Interval', 'Time', 'Start', 'End', 'Total Scan', 'Status'];
      const headerRow = worksheet.addRow(detailHeaders);
      headerRow.font = {
        bold: true,
        color: { argb: '000000' }, // Black text
      };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'E8EAF6' }, // Light blue-gray (sub-table style)
      };
      headerRow.alignment = { horizontal: 'center' };

      // Add thin border to header
      headerRow.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin', color: { argb: 'CCCCCC' } },
          left: { style: 'thin', color: { argb: 'CCCCCC' } },
          bottom: { style: 'thin', color: { argb: 'CCCCCC' } },
          right: { style: 'thin', color: { argb: 'CCCCCC' } },
        };
      });
      currentRow++;

      // Detailed interval data
      timeRotation.rotations.forEach((rotation) => {
        if (rotation.intervals && rotation.intervals.length > 0) {
          rotation.intervals.forEach((interval) => {
            // Format start and end times without timezone
            const startTime = interval.dateTimeRange?.start ?
              new Date(interval.dateTimeRange.start).toLocaleString() : '-';
            const endTime = interval.dateTimeRange?.end ?
              new Date(interval.dateTimeRange.end).toLocaleString() : '-';

            const rowData = [
              `#${intervalCounter}`,
              interval.time || '-',
              startTime,
              endTime,
              interval.rotation || 0,
              interval.rotation > 0 ? 'Completed' : 'Not Completed',
            ];

            const dataRow = worksheet.addRow(rowData);
            currentRow++;

            // Status column color coding
            const statusCell = dataRow.getCell(6); // Status column
            statusCell.font = {
              color: { argb: interval.rotation > 0 ? 'FF27ae60' : 'FFe74c3c' },
              bold: true
            };

            intervalCounter++;
          });
        }
      });
    }

    // Add border bottom pada baris terakhir untuk batas antar item
    if (currentRow > startRow) {
      const lastRow = worksheet.getRow(currentRow - 1);
      lastRow.border = {
        ...lastRow.border,
        bottom: { style: 'thin', color: { argb: '1a237e' } },
      };
    }

    // Add empty row for spacing
    worksheet.addRow([]);
    currentRow++;

    return currentRow;
  };
}

@Injectable()
export class TimeRotationGenerateDocumentService {
  private pdfGenerator: PdfGenerator;
  private excelGenerator: ExcelGenerator;

  constructor() {
    this.pdfGenerator = new PdfGenerator();
    this.excelGenerator = new ExcelGenerator();
  }

  /**
   * Generates a PDF document from time rotation data
   */
  async generatePDF(
    data: TimeAndRotation[],
    filters: TimeRotationFilters,
  ): Promise<DocumentGenerationResult> {
    const filterData = TimeRotationDataTransformer.buildFilterData(filters);
    const dateRangeFilter = TimeRotationDataTransformer.buildDateRangeFilter(filters);

    return this.pdfGenerator.generateMultipleItemsReport(
      data,
      TimeRotationDataTransformer.transformToDisplayData,
      filterData.leftFilters,
      filterData.rightFilters,
      dateRangeFilter,
      filters.timezone || {
        id: 0,
        timezone_name: 'UTC',
        gmt_offset: 0,
        country_code: 'UTC',
        active: true,
        default_latitude: 0,
        default_longitude: 0
      },
      'Time Rotation Report',
      (item) => item.checkpoint_id.toString(),
      createPdfCustomSectionRenderer,
      'time-rotation-report',
    );
  }

  /**
   * Generates an Excel spreadsheet from time rotation data
   */rou
 generateSpreadsheet(
    data: TimeAndRotation[],
    filters: TimeRotationFilters,
  ): Promise<DocumentGenerationResult> {
    const filterData = TimeRotationDataTransformer.buildFilterData(filters);
    const dateRangeFilter = TimeRotationDataTransformer.buildDateRangeFilter(filters);

    const headers = [
      'Checkpoint ID',
      'Checkpoint Name',
      'Rotation Interval',
      'Rotation Method',
      'Total Required',
      'Total Completed',
      'Compliance Rate',
      'Total Intervals',
      'Completed Intervals',
      'Interval Completion',
    ];

    return this.excelGenerator.generateMultipleItemsReport(
      data,
      headers,
      TimeRotationDataTransformer.buildExcelRowData,
      filterData.leftFilters,
      filterData.rightFilters,
      dateRangeFilter,
      filters.timezone || {
        id: 0,
        timezone_name: 'UTC',
        gmt_offset: 0,
        country_code: 'UTC',
        active: true,
        default_latitude: 0,
        default_longitude: 0
      },
      'Time Rotation Report',
      'time-rotation-report',
      createExcelCustomSectionRenderer,
    );
  }

  /**
   * Builds summary data for the report
   */
  private buildSummaryData(data: TimeAndRotation[]): Array<{ label: string; value: string }> {
    const totalCheckpoints = data.length;
    const totalRotations = data.reduce((sum, item) => sum + (item.rotations?.length || 0), 0);
    const totalIntervals = data.reduce((sum, item) =>
      sum + (item.rotations?.reduce((rotSum, rotation) => rotSum + (rotation.intervals?.length || 0), 0) || 0), 0
    );
    const completedRotations = data.reduce((sum, item) =>
      sum + (item.rotations?.reduce((rotSum, rotation) => rotSum + (rotation.completed || 0), 0) || 0), 0
    );
    const complianceRotations = data.reduce((sum, item) =>
      sum + (item.rotations?.reduce((rotSum, rotation) => rotSum + (rotation.compliance || 0), 0) || 0), 0
    );

    const overallCompliance = totalRotations > 0 ? ((complianceRotations / totalRotations) * 100).toFixed(1) : '0.0';
    const intervalCompletion = totalIntervals > 0 ? ((completedRotations / totalIntervals) * 100).toFixed(1) : '0.0';

    return [
      { label: 'Total Checkpoints', value: totalCheckpoints.toString() },
      { label: 'Total Rotations', value: totalRotations.toString() },
      { label: 'Completed Rotations', value: completedRotations.toString() },
      { label: 'Compliance Rotations', value: complianceRotations.toString() },
      { label: 'Total Intervals', value: totalIntervals.toString() },
      { label: 'Overall Compliance', value: `${overallCompliance}%` },
      { label: 'Interval Completion', value: `${intervalCompletion}%` },
    ];
  }
}
