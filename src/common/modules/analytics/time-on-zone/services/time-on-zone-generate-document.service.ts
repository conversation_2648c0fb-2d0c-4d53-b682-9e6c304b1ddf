import { Injectable } from '@nestjs/common';
import { Branch } from '../../../database/entities/branch.entity';
import { User } from '../../../database/entities/user.entity';
import { Device } from '../../../database/entities/device.entity';
import { Label } from '../../../database/entities/label.entity';
import { Zone } from '../../../database/entities/zone.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { TimeOnZoneData } from '../interfaces/time-on-zone-analytic.interface';

// Import shared utilities
import { PdfGenerator } from '../../shared/utils/pdf-generator.util';
import { ExcelGenerator } from '../../shared/utils/excel-generator.util';
import {
  DataTransformationUtils,
  DateRangeFilter,
  DocumentDisplayData,
  DocumentGenerationResult,
  FilterDisplayData,
} from '../../shared/utils/data-transformation.util';

// ===== TIME ON ZONE SPECIFIC INTERFACES =====

export interface TimeOnZoneFilters {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  zone: Zone | null;
  zone_labels: Label[];
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
}

// ===== TIME ON ZONE SPECIFIC UTILITIES =====

/**
 * Time On Zone specific data transformation utilities
 */
class TimeOnZoneDataTransformer {
  /**
   * Formats duration from seconds to human readable format
   */
  static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours === 0 && minutes === 0) {
      return `${remainingSeconds}s`;
    } else if (hours === 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  }

  static transformToDisplayData(timeOnZoneEntry: TimeOnZoneData): {
    leftColumnData: DocumentDisplayData[];
    rightColumnData: DocumentDisplayData[];
  } {
    const originalSubmittedTime = DataTransformationUtils.formatTimezoneDate(
      timeOnZoneEntry.original_submitted_time,
      timeOnZoneEntry.timezone_name,
    );

    const leftColumnData = [
      { label: 'Zone Name', value: timeOnZoneEntry.zone_name },
      {
        label: 'Entry Time',
        value: DataTransformationUtils.formatTimezoneDate(
          timeOnZoneEntry.entry,
          timeOnZoneEntry.timezone_name,
        ),
      },
      {
        label: 'Exit Time',
        value: DataTransformationUtils.formatTimezoneDate(
          timeOnZoneEntry.exit,
          timeOnZoneEntry.timezone_name,
        ),
      },
      {
        label: 'Duration',
        value: this.formatDuration(timeOnZoneEntry.duration),
      },
      {
        label: 'Timezone Name',
        value: timeOnZoneEntry.timezone_name || '-',
      },
    ];

    const rightColumnData = [
      { label: 'User Name', value: timeOnZoneEntry.user_name },
      { label: 'Device Name', value: timeOnZoneEntry.device_name },
      { label: 'Original Time', value: originalSubmittedTime },
    ];

    return { leftColumnData, rightColumnData };
  }

  static buildDataRows(timeOnZoneEntry: TimeOnZoneData): string[][] {
    const originalSubmittedTime = DataTransformationUtils.formatTimezoneDate(
      timeOnZoneEntry.original_submitted_time,
      timeOnZoneEntry.timezone_name,
    );

    return [
      ['Zone Name', timeOnZoneEntry.zone_name || '-'],
      ['User Name', timeOnZoneEntry.user_name || '-'],
      ['Device Name', timeOnZoneEntry.device_name || '-'],
      [
        'Entry Time',
        DataTransformationUtils.formatTimezoneDate(
          timeOnZoneEntry.entry,
          timeOnZoneEntry.timezone_name,
        ),
      ],
      [
        'Exit Time',
        DataTransformationUtils.formatTimezoneDate(
          timeOnZoneEntry.exit,
          timeOnZoneEntry.timezone_name,
        ),
      ],
      ['Original Time', originalSubmittedTime],
      ['Duration', this.formatDuration(timeOnZoneEntry.duration)],
      ['Timezone Name', timeOnZoneEntry.timezone_name || '-'],
    ];
  }

  static buildExcelRowData(timeOnZoneEntry: TimeOnZoneData): any[] {
    const originalSubmittedTime = DataTransformationUtils.formatTimezoneDate(
      timeOnZoneEntry.original_submitted_time,
      timeOnZoneEntry.timezone_name,
    );

    return [
      timeOnZoneEntry.zone_name,
      timeOnZoneEntry.user_name,
      timeOnZoneEntry.device_name,
      DataTransformationUtils.formatTimezoneDate(
        timeOnZoneEntry.entry,
        timeOnZoneEntry.timezone_name,
      ),
      DataTransformationUtils.formatTimezoneDate(
        timeOnZoneEntry.exit,
        timeOnZoneEntry.timezone_name,
      ),
      originalSubmittedTime,
      this.formatDuration(timeOnZoneEntry.duration),
      timeOnZoneEntry.timezone_name || '-',
    ];
  }

  static buildFilterData(filters: TimeOnZoneFilters): {
    leftFilters: FilterDisplayData[];
    rightFilters: FilterDisplayData[];
  } {
    const leftFilters = [
      {
        label: 'Start Date:',
        value: filters.startDate
          ? DataTransformationUtils.formatFilterDate(filters.startDate)
          : 'All Dates',
      },
      {
        label: 'End Date:',
        value: filters.endDate
          ? DataTransformationUtils.formatFilterDate(filters.endDate)
          : 'All Dates',
      },
      {
        label: 'Branch:',
        value: filters.branch ? filters.branch.branch_name : 'All Branches',
      },
      { label: 'User:', value: filters.user ? filters.user.name : 'All Users' },
    ];

    const rightFilters = [
      { label: 'Start Time:', value: filters.startTime || 'All Times' },
      { label: 'End Time:', value: filters.endTime || 'All Times' },
      {
        label: 'Zone:',
        value: filters.zone ? filters.zone.zone_name : 'All Zones',
      },
      {
        label: 'Device:',
        value: filters.device ? filters.device.device_name : 'All Devices',
      },
    ];

    // Add labels to filters if they exist
    if (filters.zone_labels?.length > 0) {
      leftFilters.push({
        label: 'Zone Labels:',
        value: filters.zone_labels.map(label => label.label_name).join(', '),
      });
    }

    if (filters.user_labels?.length > 0) {
      rightFilters.push({
        label: 'User Labels:',
        value: filters.user_labels.map(label => label.label_name).join(', '),
      });
    }

    if (filters.device_labels?.length > 0) {
      rightFilters.push({
        label: 'Device Labels:',
        value: filters.device_labels.map(label => label.label_name).join(', '),
      });
    }

    return { leftFilters, rightFilters };
  }

  static buildDateRangeFilter(filters: TimeOnZoneFilters): DateRangeFilter {
    return {
      startDate: filters.startDate,
      endDate: filters.endDate,
      startTime: filters.startTime,
      endTime: filters.endTime,
    };
  }
}

@Injectable()
export class TimeOnZoneGenerateDocumentService {
  private pdfGenerator = new PdfGenerator({ itemsPerPage: 3 }); // Configure 3 items per page
  private excelGenerator = new ExcelGenerator();

  constructor() {
    // Using composition pattern for both PDF and Excel generation
  }

  /**
   * Generates a PDF document from time on zone entries
   * Creates a professional report with cover page and detailed entries
   *
   * @param data - Array of time on zone entries
   * @param filters - Object containing all applied filters
   * @param timezone - Timezone object for date formatting
   * @returns Promise resolving to an object containing the PDF buffer and filename
   */
  async generatePDF(
    data: TimeOnZoneData[],
    filters: TimeOnZoneFilters,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const { leftFilters, rightFilters } =
      TimeOnZoneDataTransformer.buildFilterData(filters);
    const dateRangeFilter =
      TimeOnZoneDataTransformer.buildDateRangeFilter(filters);

    return this.pdfGenerator.generateMultipleItemsReport(
      data,
      item => TimeOnZoneDataTransformer.transformToDisplayData(item),
      leftFilters,
      rightFilters,
      dateRangeFilter,
      timezone,
      'Time On Zone Report',
      (item: TimeOnZoneData) => `${item.zone_id}-${item.user_id}-${item.device_id}`, // Generate unique identifier
      undefined, // No custom section renderer needed
      'time-on-zone-logs',
    );
  }

  /**
   * Generates a spreadsheet document from time on zone entries
   * Creates a professional report with detailed information in Excel format
   *
   * @param data - Array of time on zone entries
   * @param filters - Object containing all applied filters
   * @param timezone - Timezone object for date formatting
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   */
  async generateSpreadsheet(
    data: TimeOnZoneData[],
    filters: TimeOnZoneFilters,
    timezone: Timezone,
  ): Promise<DocumentGenerationResult> {
    const { leftFilters, rightFilters } =
      TimeOnZoneDataTransformer.buildFilterData(filters);
    const dateRangeFilter =
      TimeOnZoneDataTransformer.buildDateRangeFilter(filters);

    const headers = [
      'Zone Name',
      'User Name',
      'Device Name',
      'Entry Time',
      'Exit Time',
      'Original Time',
      'Duration',
      'Timezone Name',
    ];

    return this.excelGenerator.generateMultipleItemsReport(
      data,
      headers,
      item => TimeOnZoneDataTransformer.buildExcelRowData(item),
      leftFilters,
      rightFilters,
      dateRangeFilter,
      timezone,
      'UNIGUARD TIME ON ZONE REPORT',
      'time-on-zone-logs',
    );
  }
}
